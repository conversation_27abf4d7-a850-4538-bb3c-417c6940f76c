// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`LiquidityAmounts #getAmount0ForLiquidity gas 1`] = `352`;

exports[`LiquidityAmounts #getAmountsForLiquidity gas for price above 1`] = `481`;

exports[`LiquidityAmounts #getAmountsForLiquidity gas for price below 1`] = `502`;

exports[`LiquidityAmounts #getAmountsForLiquidity gas for price inside 1`] = `840`;

exports[`LiquidityAmounts #getLiquidityForAmount0 gas 1`] = `565`;

exports[`LiquidityAmounts #getLiquidityForAmount1 gas 1`] = `362`;

exports[`LiquidityAmounts #getLiquidityForAmount1 gas 2`] = `368`;

exports[`LiquidityAmounts #getLiquidityForAmounts gas for price above 1`] = `537`;

exports[`LiquidityAmounts #getLiquidityForAmounts gas for price below 1`] = `712`;

exports[`LiquidityAmounts #getLiquidityForAmounts gas for price inside 1`] = `1162`;
