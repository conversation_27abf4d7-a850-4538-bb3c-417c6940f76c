// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Base64 #encode gas cost of encode(<some html>) 1`] = `1497`;

exports[`Base64 #encode gas cost of encode(aLpHaBeT) 1`] = `1256`;

exports[`Base64 #encode gas cost of encode(alphabet soup) 1`] = `1727`;

exports[`Base64 #encode gas cost of encode(f) 1`] = `763`;

exports[`Base64 #encode gas cost of encode(fo) 1`] = `774`;

exports[`Base64 #encode gas cost of encode(foo) 1`] = `769`;

exports[`Base64 #encode gas cost of encode(foob) 1`] = `1004`;

exports[`Base64 #encode gas cost of encode(fooba) 1`] = `1015`;

exports[`Base64 #encode gas cost of encode(foobar) 1`] = `1010`;

exports[`Base64 #encode gas cost of encode(includes
newlines) 1`] = `1979`;

exports[`Base64 #encode gas cost of encode(test string) 1`] = `1497`;

exports[`Base64 #encode gas cost of encode(this is a test) 1`] = `1738`;

exports[`Base64 #encode gas cost of encode(this is a very long string that should cost a lot of gas to encode :)) 1`] = `6083`;

exports[`Base64 #encode gas cost of encode(😀) 1`] = `1004`;

exports[`Base64 #encode max size string (24kB) gas cost 1`] = `3731900`;
