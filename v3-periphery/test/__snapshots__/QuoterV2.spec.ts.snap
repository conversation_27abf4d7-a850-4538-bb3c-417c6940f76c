// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`QuoterV2 quotes #quoteExactInput 0 -> 2 -> 1 1`] = `277146`;

exports[`QuoterV2 quotes #quoteExactInput 0 -> 2 cross 0 tick, starting tick initialized 1`] = `123797`;

exports[`QuoterV2 quotes #quoteExactInput 0 -> 2 cross 0 tick, starting tick not initialized 1`] = `100962`;

exports[`QuoterV2 quotes #quoteExactInput 0 -> 2 cross 1 tick 1`] = `144724`;

exports[`QuoterV2 quotes #quoteExactInput 0 -> 2 cross 2 tick 1`] = `182321`;

exports[`QuoterV2 quotes #quoteExactInput 0 -> 2 cross 2 tick where after is initialized 1`] = `144762`;

exports[`QuoterV2 quotes #quoteExactInput 2 -> 0 cross 0 tick, starting tick initialized 1`] = `97654`;

exports[`QuoterV2 quotes #quoteExactInput 2 -> 0 cross 0 tick, starting tick not initialized 1`] = `93779`;

exports[`QuoterV2 quotes #quoteExactInput 2 -> 0 cross 2 1`] = `175133`;

exports[`QuoterV2 quotes #quoteExactInput 2 -> 0 cross 2 where tick after is initialized 1`] = `175141`;

exports[`QuoterV2 quotes #quoteExactInput 2 -> 1 1`] = `97329`;

exports[`QuoterV2 quotes #quoteExactInputSingle 0 -> 2 1`] = `182303`;

exports[`QuoterV2 quotes #quoteExactInputSingle 2 -> 0 1`] = `175105`;

exports[`QuoterV2 quotes #quoteExactOutput 0 -> 2 -> 1 1`] = `276746`;

exports[`QuoterV2 quotes #quoteExactOutput 0 -> 2 cross 0 tick starting tick initialized 1`] = `123352`;

exports[`QuoterV2 quotes #quoteExactOutput 0 -> 2 cross 0 tick starting tick not initialized 1`] = `100537`;

exports[`QuoterV2 quotes #quoteExactOutput 0 -> 2 cross 1 tick 1`] = `144010`;

exports[`QuoterV2 quotes #quoteExactOutput 0 -> 2 cross 2 tick 1`] = `181363`;

exports[`QuoterV2 quotes #quoteExactOutput 0 -> 2 cross 2 where tick after is initialized 1`] = `144048`;

exports[`QuoterV2 quotes #quoteExactOutput 2 -> 0 cross 1 tick 1`] = `137858`;

exports[`QuoterV2 quotes #quoteExactOutput 2 -> 0 cross 2 ticks 1`] = `175203`;

exports[`QuoterV2 quotes #quoteExactOutput 2 -> 0 cross 2 where tick after is initialized 1`] = `175197`;

exports[`QuoterV2 quotes #quoteExactOutput 2 -> 1 1`] = `97870`;

exports[`QuoterV2 quotes #quoteExactOutputSingle 0 -> 1 1`] = `105200`;

exports[`QuoterV2 quotes #quoteExactOutputSingle 1 -> 0 1`] = `98511`;
