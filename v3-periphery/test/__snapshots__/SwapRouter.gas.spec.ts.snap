// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SwapRouter gas tests #exactInput 0 -> 1 -> 2 1`] = `172498`;

exports[`SwapRouter gas tests #exactInput 0 -> 1 1`] = `107759`;

exports[`SwapRouter gas tests #exactInput 0 -> 1 minimal 1`] = `98059`;

exports[`SwapRouter gas tests #exactInput 0 -> WETH9 1`] = `127578`;

exports[`SwapRouter gas tests #exactInput 2 trades (via router) 1`] = `188814`;

exports[`SwapRouter gas tests #exactInput 3 trades (directly to sender) 1`] = `258601`;

exports[`SwapRouter gas tests #exactInput WETH9 -> 0 1`] = `106083`;

exports[`SwapRouter gas tests #exactInputSingle 0 -> 1 1`] = `107180`;

exports[`SwapRouter gas tests #exactInputSingle 0 -> WETH9 1`] = `126993`;

exports[`SwapRouter gas tests #exactInputSingle WETH9 -> 0 1`] = `105504`;

exports[`SwapRouter gas tests #exactOutput 0 -> 1 -> 2 1`] = `169275`;

exports[`SwapRouter gas tests #exactOutput 0 -> 1 1`] = `111757`;

exports[`SwapRouter gas tests #exactOutput 0 -> WETH9 1`] = `128821`;

exports[`SwapRouter gas tests #exactOutput WETH9 -> 0 1`] = `119691`;

exports[`SwapRouter gas tests #exactOutputSingle 0 -> 1 1`] = `111967`;

exports[`SwapRouter gas tests #exactOutputSingle 0 -> WETH9 1`] = `129031`;

exports[`SwapRouter gas tests #exactOutputSingle WETH9 -> 0 1`] = `114366`;

exports[`SwapRouter gas tests 3 trades (directly to sender) 1`] = `179440`;
